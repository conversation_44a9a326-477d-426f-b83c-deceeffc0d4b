//
//  NavigationDestination.swift
//  CStory
//
//  Created by NZUE on 2025/7/12.
//

import SwiftData
import SwiftUI

/// 应用中所有可导航的目的地
///
/// 定义了应用内所有可能的导航目标，支持类型安全的导航操作。
/// 每个case代表一个具体的页面或功能模块，通过关联值传递必要的参数。
///
/// ## 设计原则
/// - 类型安全：使用强类型参数，避免运行时错误
/// - 可扩展：新增页面只需添加新的case
/// - 一致性：所有导航都通过统一的枚举管理
///
/// ## 使用示例
/// ```swift
/// // 导航到交易详情页面
/// pathManager.path.append(.transactionDetailView(transactionId))
///
/// // 导航到创建分类页面
/// pathManager.path.append(.createTransactionCategory(
///   isMainCategory: true,
///   mainCategoryId: nil,
///   selectedType: .expense
/// ))
/// ```
enum NavigationDestination: Hashable {

  // MARK: - 卡片管理

  /// 卡片分类测试页面
  case cardCategoryView
  /// 卡包页面（使用 DataManagement）
  case cardBagView
  /// 选择银行测试页面
  case selectBankView(isCredit: Bool, mainCategory: CardCategoryResponse)

  // MARK: - 交易相关

  /// 交易记录页面（使用 DataManagement）
  case transactionRecordView
  /// 交易详情页面（使用正式环境的TransactionDetailView）
  case transactionDetailView(UUID)
  /// 交易退款页面（使用正式环境的TransactionRefundView）
  case transactionRefundView(transaction: TransactionModel)

  // MARK: - 分类管理

  /// 交易分类设置页面
  case transactionCategoryView
  /// 创建交易分类页面
  case createTransactionCategory(
    isMainCategory: Bool, mainCategoryId: String?, selectedType: TransactionType)
  /// 编辑交易分类页面
  case editTransactionCategoryView(categoryId: String, isMainCategory: Bool)
  /// 类别排序页面
  case categorySort(mode: CategorySortMode)

  // MARK: - 其他功能

  /// 汇率查看页面
  case currencyRateView
  /// 记账设置页面
  case transactionSettingsView
  /// 数据重置页面
  case dataResetView
  /// iCloud同步页面
  case iCloudSyncView
  /// 主题设置页面
  case themeSettingsView

  // MARK: - Hashable Implementation

  /// 计算导航目的地的哈希值
  ///
  /// 为每个导航目的地分配唯一的哈希值，确保在NavigationStack中的正确行为。
  /// 对于带参数的case，会同时考虑case类型和参数值。
  func hash(into hasher: inout Hasher) {
    switch self {
    case .cardCategoryView:
      hasher.combine(12)
    case .transactionDetailView(let transactionId):
      hasher.combine(1)
      hasher.combine(transactionId)
    case .transactionRefundView(let transaction):
      hasher.combine(2)
      hasher.combine(transaction.id)
    case .cardBagView:
      hasher.combine(10)
    case .selectBankView(let isCredit, let mainCategory):
      hasher.combine(13)
      hasher.combine(isCredit)
      hasher.combine(mainCategory.id)
    case .transactionRecordView:
      hasher.combine(11)

    case .transactionCategoryView:
      hasher.combine(6)
    case .createTransactionCategory(let isMainCategory, let mainCategoryId, let selectedType):
      hasher.combine(7)
      hasher.combine(isMainCategory)
      hasher.combine(mainCategoryId)
      hasher.combine(selectedType.rawValue)
    case .editTransactionCategoryView(let categoryId, let isMainCategory):
      hasher.combine(8)
      hasher.combine(categoryId)
      hasher.combine(isMainCategory)
    case .categorySort(let mode):
      hasher.combine(14)
      switch mode {
      case .mainCategory(let type):
        hasher.combine("main")
        hasher.combine(type.rawValue)
      case .subCategory(let mainCategoryId):
        hasher.combine("sub")
        hasher.combine(mainCategoryId)
      }
    case .currencyRateView:
      hasher.combine(9)
    case .transactionSettingsView:
      hasher.combine(15)
    case .dataResetView:
      hasher.combine(16)
    case .iCloudSyncView:
      hasher.combine(17)
    case .themeSettingsView:
      hasher.combine(18)
    }
  }

  // MARK: - Equatable Implementation

  /// 比较两个导航目的地是否相等
  ///
  /// 对于相同的case类型，会比较其关联值是否相等。
  /// 这确保了NavigationStack能够正确识别和管理导航状态。
  static func == (lhs: NavigationDestination, rhs: NavigationDestination) -> Bool {
    switch (lhs, rhs) {
    case (.cardCategoryView, .cardCategoryView):
      return true
    case (
      .transactionDetailView(let lhsTransactionId), .transactionDetailView(let rhsTransactionId)
    ):
      return lhsTransactionId == rhsTransactionId
    case (.transactionRefundView(let lhsTransaction), .transactionRefundView(let rhsTransaction)):
      return lhsTransaction.id == rhsTransaction.id
    case (.cardBagView, .cardBagView):
      return true
    case (
      .selectBankView(let lhsIsCredit, let lhsMainCategory),
      .selectBankView(let rhsIsCredit, let rhsMainCategory)
    ):
      return lhsIsCredit == rhsIsCredit && lhsMainCategory.id == rhsMainCategory.id
    case (.transactionRecordView, .transactionRecordView):
      return true

    case (.transactionCategoryView, .transactionCategoryView):
      return true
    case (
      .createTransactionCategory(let lhsIsMainCategory, let lhsMainCategoryId, let lhsSelectedType),
      .createTransactionCategory(let rhsIsMainCategory, let rhsMainCategoryId, let rhsSelectedType)
    ):
      return lhsIsMainCategory == rhsIsMainCategory && lhsMainCategoryId == rhsMainCategoryId
        && lhsSelectedType == rhsSelectedType
    case (
      .editTransactionCategoryView(let lhsCategoryId, let lhsIsMainCategory),
      .editTransactionCategoryView(let rhsCategoryId, let rhsIsMainCategory)
    ):
      return lhsCategoryId == rhsCategoryId && lhsIsMainCategory == rhsIsMainCategory
    case (.categorySort(let lhsMode), .categorySort(let rhsMode)):
      switch (lhsMode, rhsMode) {
      case (.mainCategory(let lhsType), .mainCategory(let rhsType)):
        return lhsType == rhsType
      case (.subCategory(let lhsMainCategoryId), .subCategory(let rhsMainCategoryId)):
        return lhsMainCategoryId == rhsMainCategoryId
      default:
        return false
      }
    case (.currencyRateView, .currencyRateView):
      return true
    case (.transactionSettingsView, .transactionSettingsView):
      return true
    case (.dataResetView, .dataResetView):
      return true
    case (.iCloudSyncView, .iCloudSyncView):
      return true
    case (.themeSettingsView, .themeSettingsView):
      return true
    default:
      return false
    }
  }
}

// MARK: - View Generation

extension NavigationDestination {

  /// 返回每个导航目的地对应的视图
  ///
  /// 根据导航目的地类型，返回相应的SwiftUI视图。
  /// 所有视图都通过Wrapper进行依赖注入，确保数据管理的一致性。
  ///
  /// - Parameter modelContext: SwiftData 模型上下文
  /// - Returns: 对应的 SwiftUI 视图
  ///
  /// ## 使用示例
  /// ```swift
  /// NavigationStack(path: $pathManager.path) {
  ///   HomeView()
  ///     .navigationDestination(for: NavigationDestination.self) { destination in
  ///       destination.destinationView(modelContext: modelContext)
  ///     }
  /// }
  /// ```
  @ViewBuilder
  func destinationView(modelContext: ModelContext) -> some View {
    switch self {
    case .cardCategoryView:
      CardCategoryViewWrapper()
    case .transactionDetailView(let transactionId):
      TransactionDetailViewWrapper(transactionId: transactionId)
    case .transactionRefundView(let transaction):
      TransactionRefundViewWrapper(transaction: transaction)
    case .cardBagView:
      CardBagViewWrapper()
    case .selectBankView(let isCredit, let mainCategory):
      SelectBankViewWrapper(isCredit: isCredit, mainCategory: mainCategory)
    case .transactionRecordView:
      TransactionRecordViewWrapper()

    case .transactionCategoryView:
      TransactionCategoryViewWrapper()
    case .createTransactionCategory(let isMainCategory, let mainCategoryId, let selectedType):
      CategoryFormViewWrapper(
        mode: .create(
          isMainCategory: isMainCategory, mainCategoryId: mainCategoryId, selectedType: selectedType
        ))
    case .editTransactionCategoryView(let categoryId, let isMainCategory):
      CategoryFormViewWrapper(mode: .edit(categoryId: categoryId, isMainCategory: isMainCategory))
    case .categorySort(let mode):
      CategorySortViewWrapper(mode: mode)
    case .currencyRateView:
      CurrencyRateViewWrapper()
    case .transactionSettingsView:
      TransactionSettingsViewWrapper()
    case .dataResetView:
      DataResetViewWrapper()
    case .iCloudSyncView:
      iCloudSyncViewWrapper()
    case .themeSettingsView:
      ThemeSettingsViewWrapper()
    }
  }
}

// MARK: - View Wrappers

/// 视图包装器集合
///
/// 提供所有导航目的地视图的依赖注入包装器，确保数据管理的一致性。
/// 每个包装器负责从Environment获取必要的依赖并传递给对应的视图。

// MARK: - Transaction Wrappers

/// TransactionDetailView 的包装器，用于从 Environment 获取 DataManagement
private struct TransactionDetailViewWrapper: View {
  let transactionId: UUID
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    // 根据transactionId找到对应的TransactionModel
    if let transaction = dataManager.allTransactions.first(where: { $0.id == transactionId }) {
      TransactionDetailView(transaction: transaction, dataManager: dataManager)
    } else {
      // 如果找不到交易，显示错误页面
      VStack(spacing: 20) {
        Image(systemName: "exclamationmark.triangle")
          .font(.system(size: 48))
          .foregroundColor(.orange)

        Text("交易不存在")
          .font(.title2)
          .fontWeight(.bold)

        Text("无法找到指定的交易记录")
          .font(.body)
          .foregroundColor(.secondary)
      }
      .padding()
      .navigationTitle("交易详情")
      .navigationBarTitleDisplayMode(.inline)
    }
  }
}

// MARK: - Card Wrappers

/// CardBagView 的包装器，用于从 Environment 获取 DataManagement
private struct CardBagViewWrapper: View {
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    CardBagView(dataManager: dataManager)
  }
}

/// TransactionRecordView 的包装器，用于从 Environment 获取 DataManagement
private struct TransactionRecordViewWrapper: View {
  @Environment(\.dataManager) private var dataManager
  @EnvironmentObject private var pathManager: PathManagerHelper

  var body: some View {
    TransactionRecordView(
      viewModel: TransactionRecordVM(
        dataManager: dataManager,
        onTransactionTap: { transaction in
          // 震动反馈 - 统一使用.selection强度
          dataManager.hapticManager.trigger(.selection)
          // 导航到交易详情页面
          pathManager.path.append(NavigationDestination.transactionDetailView(transaction.id))
        }
      )
    )
  }
}

/// TransactionRefundView 的包装器，用于从 Environment 获取 DataManagement
private struct TransactionRefundViewWrapper: View {
  let transaction: TransactionModel
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    TransactionRefundView(transaction: transaction, dataManager: dataManager)
  }
}

// MARK: - Category Wrappers

/// CategorySortView 的包装器，用于从 Environment 获取 DataManagement
private struct CategorySortViewWrapper: View {
  let mode: CategorySortMode
  @Environment(\.dataManager) private var dataManager
  @Environment(\.modelContext) private var modelContext

  var body: some View {
    CategorySortView(
      viewModel: CategorySortVM(
        mode: mode,
        dataManager: dataManager,
        modelContext: modelContext
      )
    )
  }
}

/// TransactionCategoryView 的包装器，用于依赖注入
private struct TransactionCategoryViewWrapper: View {
  @Environment(\.dataManager) private var dataManager
  @Environment(\.modelContext) private var modelContext

  var body: some View {
    TransactionCategoryView(
      viewModel: TransactionCategoryVM(
        dataManager: dataManager,
        modelContext: modelContext
      )
    )
  }
}

/// CategoryFormView 的包装器，用于从 Environment 获取依赖并构造 VM
struct CategoryFormViewWrapper: View {
  let mode: CategoryFormMode
  @Environment(\.dataManager) private var dataManager
  @Environment(\.modelContext) private var modelContext

  var body: some View {
    CategoryFormView(
      viewModel: CategoryFormVM(mode: mode, dataManager: dataManager, modelContext: modelContext),
      mode: mode
    )
  }
}

/// ManualTransactionView 的包装器，用于从 Environment 获取依赖并构造 VM
struct ManualTransactionViewWrapper: View {
  @Environment(\.dataManager) private var dataManager
  @Binding var showTopContent: Bool
  @Binding var showBottomContent: Bool
  var onTransactionSaved: (() -> Void)? = nil
  var onCloseRequested: (() -> Void)? = nil

  init(
    showTopContent: Binding<Bool>,
    showBottomContent: Binding<Bool>,
    onTransactionSaved: (() -> Void)? = nil,
    onCloseRequested: (() -> Void)? = nil
  ) {
    self._showTopContent = showTopContent
    self._showBottomContent = showBottomContent
    self.onTransactionSaved = onTransactionSaved
    self.onCloseRequested = onCloseRequested
  }

  var body: some View {
    ManualTransactionView(
      viewModel: ManualTransactionVM(dataManager: dataManager),
      showTopContent: $showTopContent,
      showBottomContent: $showBottomContent,
      onTransactionSaved: onTransactionSaved,
      onCloseRequested: onCloseRequested
    )
  }
}

/// AITransactionView 的包装器，用于从 Environment 获取依赖（后续 VM 也将改为构造注入）
struct AITransactionViewWrapper: View {
  @Environment(\.dataManager) private var dataManager
  @Environment(\.modelContext) private var modelContext
  @Binding var showTopContent: Bool
  @Binding var showBottomContent: Bool
  var onSwitchToManual: (() -> Void)?
  var onCloseRequested: (() -> Void)?

  init(
    showTopContent: Binding<Bool>,
    showBottomContent: Binding<Bool>,
    onSwitchToManual: (() -> Void)? = nil,
    onCloseRequested: (() -> Void)? = nil
  ) {
    self._showTopContent = showTopContent
    self._showBottomContent = showBottomContent
    self.onSwitchToManual = onSwitchToManual
    self.onCloseRequested = onCloseRequested
  }

  var body: some View {
    AITransactionView(
      viewModel: AITransactionViewModel(dataManager: dataManager, modelContext: modelContext),
      showTopContent: $showTopContent,
      showBottomContent: $showBottomContent,
      onSwitchToManual: onSwitchToManual,
      onCloseRequested: onCloseRequested
    )
  }
}

// MARK: - Setting Wrappers

/// SettingView 的包装器，用于从 Environment 获取依赖并构造 VM
struct SettingViewWrapper: View {
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    SettingView(viewModel: SettingVM(dataManager: dataManager))
  }
}

/// CardCategoryView 的包装器，用于从 Environment 获取依赖并构造 VM
private struct CardCategoryViewWrapper: View {
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    CardCategoryView(viewModel: CardCategoryVM(dataManager: dataManager))
  }
}

/// CurrencyRateView 的包装器，用于从 Environment 获取依赖并构造 VM
private struct CurrencyRateViewWrapper: View {
  @Environment(\.dataManager) private var dataManager
  @Environment(\.modelContext) private var modelContext

  var body: some View {
    CurrencyRateView(
      viewModel: CurrencyRateVM(dataManager: dataManager, modelContext: modelContext))
  }
}

/// SelectBankView 的包装器，用于从 Environment 获取依赖并构造 VM
private struct SelectBankViewWrapper: View {
  let isCredit: Bool
  let mainCategory: CardCategoryResponse
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    SelectBankView(
      viewModel: SelectBankVM(
        isCredit: isCredit, mainCategory: mainCategory, dataManager: dataManager),
      isCredit: isCredit,
      mainCategory: mainCategory
    )
  }
}

/// TransactionSettingsView 的包装器，用于从 Environment 获取依赖
private struct TransactionSettingsViewWrapper: View {
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    TransactionSettingsView()
  }
}

/// DataResetView 的包装器，用于从 Environment 获取依赖
private struct DataResetViewWrapper: View {
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    DataResetView()
  }
}

/// iCloudSyncView 的包装器，用于从 Environment 获取依赖
private struct iCloudSyncViewWrapper: View {
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    iCloudSyncView()
  }
}

/// ThemeSettingsView 的包装器，用于从 Environment 获取依赖
private struct ThemeSettingsViewWrapper: View {
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    ThemeSettingsView(viewModel: ThemeSettingsVM(dataManager: dataManager))
  }
}
