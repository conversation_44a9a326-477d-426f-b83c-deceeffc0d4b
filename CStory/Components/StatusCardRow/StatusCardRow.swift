//
//  StatusCardRow.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/17.
//

import SwiftUI

/// 通用卡片行组件
///
/// 支持左侧图标、主副标题和右侧不同类型内容的通用行组件。
/// 适用于设置页面、状态展示、信息列表等各种场景。
///
/// ## 使用示例
/// ```swift
/// // 基础文本行
/// StatusCardRow(
///   title: "iCloud 同步",
///   subtitle: "数据将自动在设备间同步",
///   rightContent: .status(text: "已开启", icon: "circle-check", color: .green)
/// )
///
/// // 带左侧图标
/// StatusCardRow(
///   leftIcon: "cloud",
///   title: "iCloud 同步",
///   subtitle: "数据将自动在设备间同步",
///   rightContent: .loading
/// )
/// ```
struct StatusCardRow: View {

  // MARK: - 右侧内容类型

  /// 右侧内容类型枚举
  enum RightContentType {
    /// 状态类型：文字 + 图标 + 颜色
    case status(text: String, icon: String, color: Color)
    /// 加载状态
    case loading
    /// 纯文本
    case text(String, color: Color = .cBlack)
    /// 图标
    case icon(String, color: Color = .cBlack)
    /// 切换器：选项数组、当前选中索引、选择回调
    case toggle(options: [String], selectedIndex: Int, onSelect: (Int) -> Void)
    /// 复选框：是否选中、选择回调
    case checkbox(isSelected: Bool, onToggle: () -> Void)
    /// 文字+图标组合
    case textWithIcon(text: String, icon: String, color: Color = .cBlack)
    /// 分段选择器：选项类型、当前选中索引、选择回调
    case segmentedPicker(options: [SegmentedOption], selectedIndex: Int, onSelect: (Int) -> Void)
  }

  // MARK: - 属性

  /// 左侧图标（可选）
  let leftIcon: String?

  /// 左侧复选框（可选）
  let leftCheckbox: ((Bool, () -> Void))?

  /// 主标题
  let title: String

  /// 副标题（可选）
  let subtitle: String?

  /// 标题后的自定义标签（可选）
  let titleTag: String?

  /// 右侧内容类型
  let rightContent: RightContentType

  // MARK: - 初始化

  /// 初始化通用卡片行
  /// - Parameters:
  ///   - leftIcon: 左侧图标名称（可选）
  ///   - leftCheckbox: 左侧复选框（可选）- 元组：(是否选中, 点击回调)
  ///   - title: 主标题
  ///   - subtitle: 副标题（可选）
  ///   - titleTag: 标题后的自定义标签（可选）
  ///   - rightContent: 右侧内容类型
  init(
    leftIcon: String? = nil,
    leftCheckbox: ((Bool, () -> Void))? = nil,
    title: String,
    subtitle: String? = nil,
    titleTag: String? = nil,
    rightContent: RightContentType
  ) {
    self.leftIcon = leftIcon
    self.leftCheckbox = leftCheckbox
    self.title = title
    self.subtitle = subtitle
    self.titleTag = titleTag
    self.rightContent = rightContent
  }

  /// 便捷初始化方法 - 兼容旧版本状态卡片
  /// - Parameters:
  ///   - title: 标题文本
  ///   - description: 描述文本
  ///   - statusText: 状态文本
  ///   - statusIcon: 状态图标名称
  ///   - statusColor: 状态颜色
  ///   - isLoading: 是否显示加载状态
  init(
    title: String,
    description: String,
    statusText: String,
    statusIcon: String,
    statusColor: Color,
    isLoading: Bool = false
  ) {
    self.leftIcon = nil
    self.leftCheckbox = nil
    self.title = title
    self.subtitle = description
    self.titleTag = nil
    self.rightContent =
      isLoading ? .loading : .status(text: statusText, icon: statusIcon, color: statusColor)
  }

  // MARK: - Body

  var body: some View {
    HStack(spacing: 12) {
      // 左侧图标区域
      if let leftIcon = leftIcon {
        Image(leftIcon)
          .font(.system(size: 20, weight: .medium))
          .foregroundColor(.cBlack)
          .frame(width: 24, height: 24)
      }

      // 左侧复选框区域
      if let leftCheckbox = leftCheckbox {
        Button(action: leftCheckbox.1) {
          Image(leftCheckbox.0 ? "circle-check" : "circle-placeholder-on")
            .font(.system(size: 20, weight: .medium))
            .foregroundColor(.cAccentBlue)
        }
      }

      // 中间内容区域
      VStack(alignment: .leading, spacing: 4) {
        HStack(spacing: 4) {
          Text(title)
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.cBlack)

          if let titleTag = titleTag {
            Text(titleTag)
              .font(.system(size: 10, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.6))
              .padding(.horizontal, 4)
              .padding(.vertical, 2)
              .background(.cAccentBlue.opacity(0.08))
              .cornerRadius(4)
          }
        }

        if let subtitle = subtitle {
          Text(subtitle)
            .font(.system(size: 12, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.6))
        }
      }

      Spacer()

      // 右侧内容区域
      rightContentView
    }
    .frame(minHeight: 40)  // 设置最小高度，确保所有行高度一致
    .padding(16)
    .background(.cWhite.opacity(0.5))
    .cornerRadius(24)
    .overlay(
      RoundedRectangle(cornerRadius: 24)
        .strokeBorder(borderColor.opacity(0.2), lineWidth: 1)
    )
  }

  // MARK: - 私有计算属性

  /// 右侧内容视图
  @ViewBuilder
  private var rightContentView: some View {
    switch rightContent {
    case .status(let text, let icon, let color):
      HStack(spacing: 8) {
        Text(text)
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(color)

        Image(icon)
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(color)
      }

    case .loading:
      ProgressView()
        .scaleEffect(0.8)

    case .text(let text, let color):
      Text(text)
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(color)

    case .icon(let icon, let color):
      Image(icon)
        .font(.system(size: 16, weight: .medium))
        .foregroundColor(color)

    case .toggle(let options, let selectedIndex, let onSelect):
      HStack(spacing: 4) {
        ForEach(Array(options.enumerated()), id: \.offset) { index, option in
          Button(action: {
            onSelect(index)
          }) {
            Text(option)
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(selectedIndex == index ? .cWhite : .cBlack)
              .padding(.horizontal, 12)
              .padding(.vertical, 6)
              .background(
                selectedIndex == index ? .cAccentBlue : .clear
              )
              .cornerRadius(8)
          }
        }
      }
      .padding(2)
      .background(.cAccentBlue.opacity(0.1))
      .cornerRadius(10)

    case .checkbox(let isSelected, let onToggle):
      Button(action: onToggle) {
        Image(isSelected ? "circle-check-fill" : "circle")
          .font(.system(size: 20, weight: .medium))
          .foregroundColor(isSelected ? .cAccentBlue : .cBlack.opacity(0.3))
      }

    case .textWithIcon(let text, let icon, let color):
      HStack(spacing: 8) {
        Text(text)
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(color)

        Image(icon)
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(color)
      }

    case .segmentedPicker(let options, let selectedIndex, let onSelect):
      SegmentedSelector(
        options: options,
        initialSelectedIndex: selectedIndex,
        compact: true,
        onSelectionChanged: onSelect
      )
    }
  }

  /// 边框颜色
  private var borderColor: Color {
    switch rightContent {
    case .status(_, _, let color):
      return color
    case .text(_, let color):
      return color
    case .icon(_, let color):
      return color
    case .textWithIcon(_, _, let color):
      return color
    case .toggle, .segmentedPicker:
      return .cAccentBlue
    case .checkbox(let isSelected, _):
      return isSelected ? .cAccentBlue : .cAccentBlue
    default:
      return .cAccentBlue
    }
  }
}

// MARK: - Preview

#Preview("基础样式") {
  VStack(spacing: 16) {
    // 纯文本行
    StatusCardRow(
      title: "设置项目",
      rightContent: .text("开启", color: .green)
    )

    // 分段选择器 - 文字
    StatusCardRow(
      title: "默认记账方式",
      rightContent: .segmentedPicker(
        options: [.text("手动"), .text("AI")],
        selectedIndex: 0,
        onSelect: { _ in }
      )
    )

    // 分段选择器 - 图标（主题）
    StatusCardRow(
      title: "主题设置",
      rightContent: .segmentedPicker(
        options: [.icon("sun"), .icon("moon"), .icon("gear")],
        selectedIndex: 1,
        onSelect: { _ in }
      )
    )

    // 左侧复选框
    StatusCardRow(
      leftCheckbox: (true, {}),
      title: "人民币",
      subtitle: "CNY · ¥",
      titleTag: "自定义",
      rightContent: .textWithIcon(text: "7.20", icon: "chevron-right", color: .cBlack.opacity(0.6))
    )

    // 文字+图标
    StatusCardRow(
      leftIcon: "star",
      title: "本位币",
      subtitle: "CNY · ¥",
      rightContent: .textWithIcon(text: "人民币", icon: "chevron-right", color: .cBlack.opacity(0.6))
    )

    // 状态展示
    StatusCardRow(
      leftIcon: "cloud",
      title: "iCloud 同步",
      subtitle: "数据将自动在设备间同步",
      rightContent: .status(text: "已开启", icon: "circle-check", color: .green)
    )
  }
  .padding()
  .background(.cLightBlue)
}
