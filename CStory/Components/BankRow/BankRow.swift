//
//  BankRow.swift
//  CStory
//
//  Created by NZUE on 2025/8/15.
//

import SwiftUI

/// 银行行视图组件
///
/// 用于显示银行信息的可复用组件，支持银行图标、名称显示和点击交互。
/// 使用 IconView 统一处理图标显示逻辑，提供一致的用户体验。
///
/// ## 功能特性
/// - 银行图标显示（支持自动降级到占位符）
/// - 银行名称显示
/// - 点击交互支持
/// - 触觉反馈
/// - 统一的视觉样式
///
/// ## 使用示例
/// ```swift
/// BankRow(
///   bank: bankData,
///   bankImage: imageData,
///   defaultBankIcon: defaultImageData,
///   action: {
///     // 处理银行选择
///   }
/// )
/// ```
struct BankRow: View {
  
  // MARK: - Properties
  
  /// 银行数据
  let bank: CardSubCategoryResponse
  /// 银行图标数据
  let bankImage: Data?
  /// 默认银行图标数据
  let defaultBankIcon: Data?
  /// 点击回调
  let action: () -> Void
  
  // MARK: - Environment
  
  /// 数据管理器
  @Environment(\.dataManager) private var dataManager
  
  // MARK: - Body
  
  var body: some View {
    Button(action: {
      dataManager.hapticManager.trigger(.impactLight)
      action()
    }) {
      HStack(spacing: 15) {
        // 银行图标 - 使用 IconView 简化显示逻辑
        IconView(
          viewModel: .optionalImage(
            bankImage ?? defaultBankIcon,
            size: 44,
            style: .rounded
          )
        )

        // 银行名称
        Text(bank.displayName)
          .font(.system(size: 17, weight: .regular))
          .foregroundColor(.cBlack)
          .multilineTextAlignment(.leading)
          .lineLimit(1)

        Spacer()
      }
      .padding(.horizontal, 20)
      .padding(.vertical, 14)
      .background(.cWhite.opacity(0.5))
      .contentShape(Rectangle())
      .overlay(
        // 底部分隔线
        Rectangle()
          .fill(.cBlack.opacity(0.05))
          .frame(height: 0.5)
          .padding(.leading, 79),  // 对齐银行名称
        alignment: .bottom
      )
    }
    .buttonStyle(PlainButtonStyle())
  }
}

// MARK: - Preview

#Preview {
  VStack(spacing: 0) {
    BankRow(
      bank: CardSubCategoryResponse(
        name: "icbc",
        displayName: "中国工商银行"
      ),
      bankImage: nil,
      defaultBankIcon: nil,
      action: {
        print("银行被选择")
      }
    )
    
    BankRow(
      bank: CardSubCategoryResponse(
        name: "cmb",
        displayName: "招商银行"
      ),
      bankImage: nil,
      defaultBankIcon: nil,
      action: {
        print("银行被选择")
      }
    )
    
    BankRow(
      bank: CardSubCategoryResponse(
        name: "abc",
        displayName: "中国农业银行"
      ),
      bankImage: nil,
      defaultBankIcon: nil,
      action: {
        print("银行被选择")
      }
    )
  }
  .background(.cLightBlue)
  .environment(\.dataManager, DataManagement(
    cards: [],
    mainCategories: [],
    subCategories: [],
    currencies: [],
    recentTransactions: [],
    allTransactions: [],
    chatMessages: []
  ))
}
