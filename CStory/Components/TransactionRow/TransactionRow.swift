//
//  TransactionRow.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/18.
//

import SwiftUI

/// 交易记录行视图
///
/// 显示单笔交易信息的可交互行组件，遵循MVVM架构模式。
/// 通过TransactionRowVM处理所有数据格式化和业务逻辑，视图专注于UI渲染。
///
/// 该组件支持显示交易分类、金额、卡片信息、时间等完整交易信息，
/// 并提供退款标签、多卡片标签等状态标识功能。
///
/// ## 主要功能
/// - 交易分类图标和名称显示
/// - 交易金额和优惠信息
/// - 卡片信息和时间显示
/// - 状态标签管理
/// - 点击交互处理
///
/// ## 使用示例
/// ```swift
/// let viewModel = TransactionRowVM(from: transactionModel)
/// viewModel.onTap = { /* 处理点击 */ }
/// TransactionRow(viewModel: viewModel)
/// ```
///
/// - Author: 咩咩
/// - Since: 2025.7.18
/// - Note: 该组件是无状态的，所有状态管理都在ViewModel中
struct TransactionRow: View {

  // MARK: - Properties

  /// 交易行视图模型
  ///
  /// 管理交易显示数据和交互逻辑的视图模型实例。
  /// 包含交易分类、金额、卡片信息、状态标签等所有显示相关数据。
  /// 视图通过观察此对象的变化来自动更新UI。
  @ObservedObject var viewModel: TransactionRowVM

  // MARK: - 主体视图

  var body: some View {
    Button(action: {
      viewModel.onTap?()
    }) {
      HStack(alignment: .center) {
        // MARK: 左侧内容 (图标和分类)
        HStack(spacing: 12) {
          if let icon = viewModel.icon {
            IconView(
              viewModel: IconViewVM(
                icon: icon,
                size: 40,
                fontSize: 20,
                backgroundColor: .clear,
                cornerRadius: 12
              ))

          }

          VStack(alignment: .leading, spacing: 8) {
            HStack(alignment: .center, spacing: 4) {
              Text(viewModel.categoryName)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.cBlack)

              if viewModel.hasRefundTag {
                TagView(text: "退款", color: .cAccentGreen)
              }

              if viewModel.hasDiscountTag {
                TagView(text: "优惠", color: .orange)
              }
            }

            Text(viewModel.formattedTime)
              .font(.system(size: 12, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.6))
          }
        }

        Spacer()

        // MARK: 右侧内容 (金额和附加信息)
        VStack(alignment: .trailing, spacing: 4) {
          // 金额显示
          Group {
            if let originalAmount = viewModel.originalAmountForStrikethrough {
              // 有优惠或退款，显示双行
              HStack(spacing: 4) {
                DisplayCurrencyView.size12(symbol: viewModel.currencySymbol, amount: originalAmount)
                  .simpleFormat().color(.gray).strikethrough()

                DisplayCurrencyView.size15(
                  symbol: viewModel.currencySymbol, amount: viewModel.displayAmount
                )
                .simpleFormat()
                .showingPlusSign(viewModel.shouldShowPlusSign)
                .color(viewModel.amountColor)
              }
            } else {
              // 正常情况，显示单行
              DisplayCurrencyView.size15(
                symbol: viewModel.currencySymbol, amount: viewModel.displayAmount
              )
              .simpleFormat()
              .showingPlusSign(viewModel.shouldShowPlusSign)
              .color(viewModel.amountColor)
            }
          }

          // 附加信息
          if let cardName = viewModel.relatedCardName {
            Text(cardName)
              .font(.system(size: 12, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.4))
          }

          if let balanceText = viewModel.balanceText {
            Text(balanceText)
              .font(.system(size: 12, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.6))
          }
        }
      }
      .padding(.top, 12)
      .padding(.bottom, 12)
      .padding(.leading, 12)
      .padding(.trailing, 16)
      .background(.cWhite.opacity(0.5))
      .cornerRadius(24)
      .overlay(
        RoundedRectangle(cornerRadius: 24)
          .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: 1)
      )
    }
  }
}

// MARK: - Private Components

/// 交易状态标签视图
///
/// 显示交易状态标识的小型标签组件，用于标示退款、优惠等特殊状态。
/// 采用圆角矩形设计，支持不同颜色主题以区分不同状态类型。
///
/// - Parameters:
///   - text: 标签显示文字（如"退款"、"优惠"）
///   - color: 标签背景颜色主题，将自动应用70%透明度
/// - Returns: 配置好的标签视图
/// - Note: 该组件为私有组件，仅在TransactionRow内部使用
private func TagView(text: String, color: Color) -> some View {
  Text(text)
    .font(.system(size: 10, weight: .medium))
    .foregroundColor(.white)
    .padding(.horizontal, 4)
    .padding(.vertical, 1)
    .background(color.opacity(0.7))
    .cornerRadius(4)
}

// MARK: - Preview Provider

/// 交易行组件预览
///
/// 提供多种典型交易场景的预览，展示组件在不同状态下的显示效果。
/// 包含普通收支、优惠退款、转账等多种交易类型的视觉效果验证。
#if DEBUG
  struct TransactionRow_Previews: PreviewProvider {
    static var previews: some View {
      ScrollView {
        VStack(spacing: 10) {
          Text("交易记录预览")
            .font(.largeTitle)
            .bold()
            .padding()

          // 场景1: 普通支出交易
          TransactionRow(
            viewModel: .init(
              icon: .emoji("🛍️"),  // 购物图标
              categoryName: "购物-日常用品",
              formattedTime: "14:35",
              displayAmount: -128.50,
              amountColor: .black,
              currencySymbol: "¥",
              hasRefundTag: false,
              hasDiscountTag: false
            ))

          // 场景2: 普通收入
          TransactionRow(
            viewModel: .init(
              icon: .emoji("💰"),  // 收入图标
              categoryName: "职业收入-工资",
              formattedTime: "09:00",
              displayAmount: 8000.00,
              amountColor: .green,  // 假设收入为绿色
              currencySymbol: "¥",
              hasRefundTag: false,
              hasDiscountTag: false,
              relatedCardName: "招商银行储蓄卡"  // 显示卡片名称
            ))

          // 场景3: 带有优惠和退款的复杂支出
          TransactionRow(
            viewModel: .init(
              icon: .emoji("🍱"),  // 餐饮图标
              categoryName: "餐饮-午餐",
              formattedTime: "12:10",
              displayAmount: -25.00,
              amountColor: .black,
              originalAmountForStrikethrough: -30.00,  // 带有划线价
              currencySymbol: "¥",
              hasRefundTag: false,  // 假设只有优惠
              hasDiscountTag: true,
              relatedCardName: "微信零钱",
              balanceText: "余额: ¥1024.50"  // 显示交易后余额
            ))

          // 场景4: 带有退款标签的交易
          TransactionRow(
            viewModel: .init(
              icon: .emoji("↩️"),  // 退款图标
              categoryName: "购物-衣物",
              formattedTime: "昨天 18:20",
              displayAmount: -199.00,
              amountColor: .black,
              originalAmountForStrikethrough: -299.00,
              currencySymbol: "¥",
              hasRefundTag: true,  // 显示退款标签
              hasDiscountTag: false,
              balanceText: "信用卡欠款: ¥2500.00"
            ))

          // 场景5: 转账交易
          TransactionRow(
            viewModel: .init(
              icon: .emoji("🔄"),  // 转账图标
              categoryName: "转账",
              formattedTime: "15:00",
              displayAmount: 500.00,
              amountColor: .gray,  // 转账通常为中性色
              currencySymbol: "¥",
              hasRefundTag: false,
              hasDiscountTag: false,
              relatedCardName: "储蓄卡 > 信用卡"
            ))

        }
        .padding(.horizontal)
      }
      .background(Color(.systemGroupedBackground))
    }
  }
#endif
