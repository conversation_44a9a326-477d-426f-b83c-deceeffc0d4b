//
//  ThemeSettingsVM.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/13.
//

import SwiftUI

/// 主题设置页面视图模型
///
/// 简化的主题设置管理，使用@AppStorage进行持久化。
final class ThemeSettingsVM: ObservableObject {

  // MARK: - Published Properties

  /// 当前选中的主题模式，使用@AppStorage自动持久化
  @AppStorage(ThemeHelper.themeKey) var selectedTheme: String = AppThemeMode.system.rawValue

  // MARK: - Dependencies

  /// 数据管理器，提供统一的数据访问接口
  let dataManager: DataManagement

  // MARK: - Computed Properties

  /// 当前主题模式枚举
  var currentThemeMode: AppThemeMode {
    AppThemeMode(rawValue: selectedTheme) ?? .system
  }

  /// 所有主题选项
  var themeOptions: [AppThemeMode] {
    AppThemeMode.allCases
  }

  // MARK: - Initialization

  /// 初始化主题设置视图模型
  init(dataManager: DataManagement) {
    self.dataManager = dataManager
  }

  // MARK: - Public Methods

  /// 选择主题模式
  @MainActor
  func selectTheme(_ theme: AppThemeMode) {
    // 触发触觉反馈
    dataManager.hapticManager.trigger(.selection)

    // 更新选中状态（@AppStorage会自动保存）
    selectedTheme = theme.rawValue
  }

  /// 获取主题选项的选中状态
  func isThemeSelected(_ theme: AppThemeMode) -> Bool {
    return currentThemeMode == theme
  }

}
