//
//  TransactionCategoryView.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftData
import SwiftUI

// MARK: - 通用分类视图模型

/// 通用分类视图模型
///
/// 管理交易分类选择状态，支持支出和收入两种类型。
/// 支持展开/收起子分类列表，并通过回调通知分类变化。
@MainActor
final class CategoryViewModel: ObservableObject {
  /// 选中的主分类ID
  @Published var mainCategoryId: String?
  /// 选中的子分类ID
  @Published var subCategoryId: String?
  /// 是否显示子分类
  @Published var showSubCategories: Bool = false

  /// 分类选择回调
  private let onCategorySelected: (String?, String?) -> Void
  /// 分类类型（用于调试信息）
  private let categoryType: String

  init(categoryType: String, onCategorySelected: @escaping (String?, String?) -> Void) {
    self.categoryType = categoryType
    self.onCategorySelected = onCategorySelected
  }

  /// 选择主分类
  func selectMainCategory(_ category: TransactionMainCategoryModel) {
    if mainCategoryId == category.id {
      // 切换子分类显示状态
      showSubCategories.toggle()
      subCategoryId = nil
    } else {
      // 选择新的主分类
      mainCategoryId = category.id
      subCategoryId = nil
      showSubCategories = true
    }

    #if DEBUG
      let subCategoriesCount = category.subCategories?.count ?? 0
      print(
        "【\(categoryType)分类】选择主分类: \(category.name) (ID: \(category.id)), 子分类数量: \(subCategoriesCount)"
      )
      if let subCategories = category.subCategories {
        let sortedSubs = subCategories.sorted { $0.order < $1.order }
        print(
          "【\(categoryType)分类】子分类排序: \(sortedSubs.map { "\($0.name)(order:\($0.order))" }.joined(separator: ", "))"
        )
      }
    #endif

    onCategorySelected(category.id, subCategoryId)
  }

  /// 选择子分类
  func selectSubCategory(_ subCategory: TransactionSubCategoryModel) {
    subCategoryId = subCategory.id
    #if DEBUG
      print(
        "【\(categoryType)分类】选择子分类: \(subCategory.name) (ID: \(subCategory.id), order: \(subCategory.order))"
      )
    #endif
    onCategorySelected(mainCategoryId, subCategory.id)
  }
}

// MARK: - 主分类网格视图
struct CategoryGridView: View {
  let categories: [TransactionMainCategoryModel]
  let selectedId: String?
  let selectedMainCategory: TransactionMainCategoryModel?
  let selectedSubId: String?
  let showSubCategories: Bool
  let onSelect: (TransactionMainCategoryModel) -> Void
  let onSelectSub: (TransactionSubCategoryModel) -> Void

  var body: some View {
    LazyVStack(alignment: .leading, spacing: 12) {
      let rows = stride(from: 0, to: categories.count, by: 5).map {
        Array(categories[$0..<Swift.min($0 + 5, categories.count)])
      }
      ForEach(Array(rows.enumerated()), id: \.offset) { rowIndex, row in
        CategoryRow(
          row: row,
          selectedId: selectedId,
          selectedMainCategory: selectedMainCategory,
          selectedSubId: selectedSubId,
          showSubCategories: showSubCategories,
          onSelect: onSelect,
          onSelectSub: onSelectSub
        )
      }
    }
    .padding(.top, 8)
  }
}

// MARK: - Category Row View
private struct CategoryRow: View {
  let row: [TransactionMainCategoryModel]
  let selectedId: String?
  let selectedMainCategory: TransactionMainCategoryModel?
  let selectedSubId: String?
  let showSubCategories: Bool
  let onSelect: (TransactionMainCategoryModel) -> Void
  let onSelectSub: (TransactionSubCategoryModel) -> Void
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    VStack(spacing: 12) {
      HStack(spacing: 12) {
        ForEach(row) { category in
          CategoryItemView(
            icon: category.icon,
            name: category.name,
            isSelected: category.id == selectedId
          )
          .onTapGesture {
            dataManager.hapticManager.trigger(.selection)
            onSelect(category)
          }
        }
        if row.count < 5 {
          ForEach(0..<(5 - row.count), id: \.self) { _ in
            Spacer().frame(maxWidth: .infinity)
          }
        }
      }
      .padding(.horizontal, 12)

      if shouldShowSubCategories(for: row) {
        SubCategoriesListView(
          subCategories: selectedMainCategory?.subCategories ?? [],
          selectedId: selectedSubId,
          onSelect: onSelectSub
        )
      }
    }
  }

  private func shouldShowSubCategories(for row: [TransactionMainCategoryModel]) -> Bool {
    showSubCategories && selectedMainCategory != nil
      && row.contains(where: { $0.id == selectedMainCategory?.id })
      && !(selectedMainCategory?.subCategories?.isEmpty ?? true)
  }
}

// MARK: - 子分类列表视图
struct SubCategoriesListView: View {
  let subCategories: [TransactionSubCategoryModel]
  let selectedId: String?
  let onSelect: (TransactionSubCategoryModel) -> Void
  @Environment(\.dataManager) private var dataManager

  /// 缓存排序后的子分类，提高性能
  @State private var sortedSubCategories: [TransactionSubCategoryModel] = []

  var body: some View {
    ScrollView(.horizontal, showsIndicators: false) {
      LazyHStack(spacing: 20) {
        ForEach(sortedSubCategories) { subCategory in
          CategoryItemView(
            icon: subCategory.icon,
            name: subCategory.name,
            isSelected: subCategory.id == selectedId
          )
          .onTapGesture {
            dataManager.hapticManager.trigger(.selection)
            onSelect(subCategory)
          }
        }
      }
      .padding(.vertical, 8)
      .padding(.horizontal, 16)
    }
    .background(.cWhite.opacity(0.5))
    .cornerRadius(24)
    .overlay(
      RoundedRectangle(cornerRadius: 24)
        .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
    .padding(.horizontal, 12)
    .onAppear {
      sortedSubCategories = subCategories.sorted { $0.order < $1.order }
    }
    .onChange(of: subCategories) { _, newSubCategories in
      sortedSubCategories = newSubCategories.sorted { $0.order < $1.order }
    }
  }
}

// MARK: - Category Item View
struct CategoryItemView: View {
  let icon: IconType
  let name: String
  let isSelected: Bool

  var body: some View {
    VStack {
      IconView(
        viewModel: IconViewVM(
          icon: icon,
          size: 44,
          fontSize: 28,
          backgroundColor: .cAccentBlue.opacity(0.05),
          cornerRadius: 24,
          isSelected: isSelected
        )
      )
      .shadow(color: .cBlack.opacity(0.05), radius: 8, x: 0, y: 2)

      Text(name)
        .font(.system(size: 13, weight: .regular))
        .foregroundColor(.cBlack)
    }
    .frame(maxWidth: .infinity)

  }

}

// MARK: - 通用分类选择视图
struct CategorySelectionView: View {
  @Environment(\.modelContext) private var context
  @ObservedObject private var viewModel: CategoryViewModel

  let mainCategories: [TransactionMainCategoryModel]
  let categoryType: String

  private var selectedMainCategory: TransactionMainCategoryModel? {
    mainCategories.first { $0.id == viewModel.mainCategoryId }
  }

  init(
    categoryType: String,
    mainCategories: [TransactionMainCategoryModel],
    onCategorySelected: @escaping (String?, String?) -> Void
  ) {
    self.categoryType = categoryType
    self.mainCategories = mainCategories
    self.viewModel = CategoryViewModel(
      categoryType: categoryType,
      onCategorySelected: onCategorySelected
    )
  }

  var body: some View {
    ScrollView {
      CategoryGridView(
        categories: mainCategories,
        selectedId: viewModel.mainCategoryId,
        selectedMainCategory: selectedMainCategory,
        selectedSubId: viewModel.subCategoryId,
        showSubCategories: viewModel.showSubCategories,
        onSelect: viewModel.selectMainCategory,
        onSelectSub: viewModel.selectSubCategory
      )
      Spacer()
    }
    .scrollBounceBehavior(.basedOnSize)
    .scrollIndicators(.hidden)
    .onAppear {
      if viewModel.mainCategoryId == nil, let firstCategory = mainCategories.first {
        viewModel.selectMainCategory(firstCategory)
      }
    }
  }
}

// MARK: - 支出分类视图
struct ExpenseCategoryView: View {
  let mainCategories: [TransactionMainCategoryModel]
  let onCategorySelected: (String?, String?) -> Void

  init(
    mainCategories: [TransactionMainCategoryModel],
    onCategorySelected: @escaping (String?, String?) -> Void
  ) {
    self.mainCategories = mainCategories
    self.onCategorySelected = onCategorySelected
  }

  var body: some View {
    CategorySelectionView(
      categoryType: "支出",
      mainCategories: mainCategories,
      onCategorySelected: onCategorySelected
    )
  }
}

// MARK: - 收入分类视图
struct IncomeCategoryView: View {
  let mainCategories: [TransactionMainCategoryModel]
  let onCategorySelected: (String?, String?) -> Void

  init(
    mainCategories: [TransactionMainCategoryModel],
    onCategorySelected: @escaping (String?, String?) -> Void
  ) {
    self.mainCategories = mainCategories
    self.onCategorySelected = onCategorySelected
  }

  var body: some View {
    CategorySelectionView(
      categoryType: "收入",
      mainCategories: mainCategories,
      onCategorySelected: onCategorySelected
    )
  }
}

// MARK: - 转账分类视图
struct TransferCategoryView: View {
  @Environment(\.modelContext) private var context
  @Environment(\.dataManager) private var dataManager
  let cards: [CardModel]

  @Binding var isTransferOutgoing: Bool
  @Binding var isTransferCardSelected: Bool
  @Binding var controlBarState: ControlBarState
  @Binding var selectedTransferSourceCardId: UUID?
  @Binding var selectedTransferDestinationCardId: UUID?

  @State private var rotationAngle: Double = 0

  private func getCardInfo(for id: UUID?) -> CardModel? {
    guard let id = id else { return nil }
    return cards.first { $0.id == id }
  }

  private func swapCards() {
    dataManager.hapticManager.trigger(.impactMedium)
    withAnimation(.spring(response: 0.3)) {
      rotationAngle += 180
      let tempId = selectedTransferSourceCardId
      selectedTransferSourceCardId = selectedTransferDestinationCardId
      selectedTransferDestinationCardId = tempId
    }
  }

  private func selectSourceCard() {
    dataManager.hapticManager.trigger(.selection)
    withAnimation(.spring()) {
      isTransferOutgoing = true
      controlBarState = .selectCard
    }
  }

  private func selectDestinationCard() {
    dataManager.hapticManager.trigger(.selection)
    withAnimation(.spring()) {
      isTransferOutgoing = false
      controlBarState = .selectCard
    }
  }

  // 简化选中状态判断
  private var isSourceSelected: Bool {
    controlBarState == .selectCard && isTransferOutgoing
  }

  private var isDestinationSelected: Bool {
    controlBarState == .selectCard && !isTransferOutgoing
  }

  var body: some View {
    VStack(spacing: -8) {
      // 转出卡片按钮
      TransferCardButton(
        title: "转出卡片",
        card: getCardInfo(for: selectedTransferSourceCardId),
        isSelected: isSourceSelected
      ) {
        selectSourceCard()
      }

      // 转账箭头
      Button(action: {
        swapCards()
      }) {
        Image(systemName: "arrow.up.arrow.down")
          .font(.system(size: 18, weight: .medium))
          .foregroundColor(.cAccentBlue.opacity(0.8))
          .frame(width: 40, height: 40)
          .background(.cWhite.opacity(0.5))
          .cornerRadius(20)
          .shadow(color: .cBlack.opacity(0.08), radius: 8, x: 0, y: 2)
          .overlay(
            RoundedRectangle(cornerRadius: 20)
              .strokeBorder(.cAccentBlue.opacity(0.15), lineWidth: 1.5)
          )
          .rotationEffect(.degrees(rotationAngle))
          .scaleEffect(1.05)  // 稍微放大增强视觉层次
      }
      .zIndex(2)

      // 转入卡片按钮
      TransferCardButton(
        title: "转入卡片",
        card: getCardInfo(for: selectedTransferDestinationCardId),
        isSelected: isDestinationSelected
      ) {
        selectDestinationCard()
      }

      Spacer()
    }
    .padding(.horizontal, 16)
    .padding(.top, 8)

  }

  // 提取的转账卡片按钮组件
  private struct TransferCardButton: View {
    let title: String
    let card: CardModel?
    let isSelected: Bool
    let action: () -> Void
    @Environment(\.dataManager) private var dataManager

    var body: some View {
      Button(action: {
        dataManager.hapticManager.trigger(.selection)
        action()
      }) {
        VStack(alignment: .leading, spacing: 12) {
          HStack(spacing: 8) {
            if let card = card {
              Text(card.name)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.cBlack)

            

              
            } else {
              Text("请选择\(title)")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.cBlack)
            }

            Spacer()
          }

          HStack(alignment: .center, spacing: 12) {
            // 统一显示图标区域，保持布局一致性
            IconView(
              viewModel: IconViewVM.optionalImage(
                card?.bankLogo,
                size: 44,
                style: IconStyle(
                  backgroundColor: .cAccentBlue.opacity(0.05),
                  cornerRadius: 12
                )
              )
            )

            VStack(alignment: .leading, spacing: 4) {
              if let card = card {
                  Text(card.bankName)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.cBlack)
                Text(card.isCredit ? "信用卡" : "储蓄卡")
                  .font(.system(size: 12, weight: .regular))
                  .foregroundColor(.cBlack.opacity(0.5))
              } else {
                // 没有卡片时显示提示信息
                Text("选择一张卡片")
                  .font(.system(size: 14, weight: .medium))
                  .foregroundColor(.cBlack.opacity(0.4))
              }
            }

            Spacer()
              if let card = card {
                  HStack(alignment: .firstTextBaseline, spacing: 4) {
                      Text(NumberFormatService.shared.formatAmount(card.balance))
                          .font(.system(size: 24, weight: .semibold))
                          .foregroundColor(.cBlack)
                      Text(card.currency)
                          .font(.system(size: 14, weight: .medium))
                          .foregroundColor(.cBlack.opacity(0.5))
                  }
              }
          }
        }
        .padding(16)
        .frame(minHeight: 84)  // 确保统一的最小高度
        .background(isSelected ? .cAccentBlue.opacity(0.06) : .cWhite.opacity(0.5))
        .cornerRadius(24)
        .overlay(
          RoundedRectangle(cornerRadius: 24)
            .strokeBorder(
              isSelected ? .cAccentBlue.opacity(0.5) : .cAccentBlue.opacity(0.08),
              lineWidth: isSelected ? 1.5 : 1)
        )
       
      }
      .buttonStyle(PlainButtonStyle())  // 防止默认按钮样式干扰
      .scaleEffect(isSelected ? 1.02 : 1.0)
      .animation(.easeInOut(duration: 0.3), value: isSelected)
    }
  }
}
